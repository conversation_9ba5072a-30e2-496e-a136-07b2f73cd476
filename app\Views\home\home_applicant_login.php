<?= $this->extend('templates/home_template') ?>

<?= $this->section('content') ?>

<!-- Portal Activation Notice Modal -->
<div class="modal fade" id="portalNoticeModal" tabindex="-1" aria-labelledby="portalNoticeModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content portal-notice-modal">
            <div class="modal-header border-0 text-center">
                <div class="w-100">
                    <div class="notice-icon mb-3">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h2 class="modal-title notice-title" id="portalNoticeModalLabel">Portal Activation Notice</h2>
                </div>
            </div>
            <div class="modal-body text-center">
                <p class="notice-message">This portal will be activated on <strong>Monday 30th June 2025</strong></p>

                <div class="notice-countdown">
                    <div class="countdown-item">
                        <span id="days" class="countdown-number">0</span>
                        <span class="countdown-label">Days</span>
                    </div>
                    <div class="countdown-item">
                        <span id="hours" class="countdown-number">0</span>
                        <span class="countdown-label">Hours</span>
                    </div>
                    <div class="countdown-item">
                        <span id="minutes" class="countdown-number">0</span>
                        <span class="countdown-label">Minutes</span>
                    </div>
                    <div class="countdown-item">
                        <span id="seconds" class="countdown-number">0</span>
                        <span class="countdown-label">Seconds</span>
                    </div>
                </div>

                <p class="notice-footer">Please check back after the activation date to access the portal.</p>
            </div>
        </div>
    </div>
</div>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Applicant Login</h3>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('swal_icon')): ?>
                        <div class="alert alert-<?= session()->getFlashdata('swal_icon') == 'success' ? 'success' : (session()->getFlashdata('swal_icon') == 'error' ? 'danger' : 'warning') ?>">
                            <strong><?= session()->getFlashdata('swal_title') ?></strong><br>
                            <?= session()->getFlashdata('swal_text') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('applicant/login') ?>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-red">Login</button>
                    </div>

                    <?= form_close() ?>

                    <div class="text-center mt-3">
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">Forgot Password?</a>
                    </div>

                    <div class="text-center mt-3">
                        <p>Don't have an account? <a href="<?= base_url('applicant/register') ?>" class="text-decoration-none">Register here</a></p>
                        <a href="<?= base_url('/') ?>" class="text-decoration-none">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Modal -->
<div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="forgotPasswordModalLabel">Forgot Password</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="<?= base_url('applicant/forgot-password') ?>" method="post">
        <?= csrf_field() ?>
        <div class="modal-body">
          <div class="mb-3">
            <label for="forgot_email" class="form-label">Enter your registered email address</label>
            <input type="email" class="form-control" id="forgot_email" name="email" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Send Reset Code</button>
        </div>
      </form>
    </div>
  </div>
</div>
<?= $this->section('styles') ?>
<style>
/* Portal Notice Modal Styles */
.modal-backdrop {
    background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.95) 0%, rgba(208, 13, 0, 0.95) 40%, rgba(0, 0, 0, 0.98) 100%);
    backdrop-filter: blur(5px);
}

.portal-notice-modal {
    background: white;
    border-radius: 20px;
    border: 3px solid #F00F00;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.portal-notice-modal::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(to bottom left, #F00F00, #D00D00, #000000);
    border-radius: 20px;
    z-index: -1;
}

.portal-notice-modal .modal-header {
    background: transparent;
    padding: 30px 30px 0 30px;
}

.portal-notice-modal .modal-body {
    padding: 20px 30px 40px 30px;
}

.notice-icon {
    font-size: 4rem;
    color: #F00F00;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.notice-title {
    color: #F00F00;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.notice-message {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.6;
}

.notice-message strong {
    color: #F00F00;
    font-weight: bold;
}

.notice-countdown {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.countdown-item {
    background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 60%, #000000 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    min-width: 80px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.countdown-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.countdown-label {
    display: block;
    font-size: 0.9rem;
    margin-top: 5px;
    opacity: 0.9;
}

.notice-footer {
    color: #666;
    font-size: 1rem;
    margin-top: 20px;
    font-style: italic;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .portal-notice-modal .modal-header {
        padding: 20px 20px 0 20px;
    }

    .portal-notice-modal .modal-body {
        padding: 15px 20px 30px 20px;
    }

    .notice-title {
        font-size: 2rem;
    }

    .notice-message {
        font-size: 1.1rem;
    }

    .countdown-item {
        min-width: 70px;
        padding: 12px 15px;
    }

    .countdown-number {
        font-size: 1.5rem;
    }
}

/* Disable page content when modal is shown */
.modal-open .container:not(.modal-container) {
    pointer-events: none;
    user-select: none;
    filter: blur(2px);
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Portal Notice Modal Management
let portalModal;
let countdownInterval;

// Countdown Timer for Portal Activation
function updateCountdown() {
    // Set the activation date: Monday 30th June 2025 at 00:00:00
    const activationDate = new Date('2025-06-30T00:00:00').getTime();
    const now = new Date().getTime();
    const timeLeft = activationDate - now;

    if (timeLeft > 0) {
        // Calculate time units
        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

        // Update countdown display
        document.getElementById('days').textContent = days;
        document.getElementById('hours').textContent = hours;
        document.getElementById('minutes').textContent = minutes;
        document.getElementById('seconds').textContent = seconds;
    } else {
        // Portal should be activated - hide the modal
        if (portalModal) {
            portalModal.hide();
        }
        clearInterval(countdownInterval);
        enablePageContent();
    }
}

// Disable page content
function disablePageContent() {
    // Disable all form elements
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, button, select, textarea');
        inputs.forEach(input => {
            input.disabled = true;
        });
    });

    // Disable all links except navigation
    const links = document.querySelectorAll('a:not(.navbar a):not(.nav a)');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            return false;
        });
    });
}

// Enable page content
function enablePageContent() {
    // Enable all form elements
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, button, select, textarea');
        inputs.forEach(input => {
            input.disabled = false;
        });
    });

    // Remove blur effect
    document.querySelector('.container').style.filter = 'none';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap modal
    portalModal = new bootstrap.Modal(document.getElementById('portalNoticeModal'), {
        backdrop: 'static',
        keyboard: false
    });

    // Show the modal immediately
    portalModal.show();

    // Disable page content
    disablePageContent();

    // Start countdown
    countdownInterval = setInterval(updateCountdown, 1000);
    updateCountdown(); // Initial call

    // Prevent modal from being closed
    document.getElementById('portalNoticeModal').addEventListener('hide.bs.modal', function (e) {
        // Check if portal should be activated
        const activationDate = new Date('2025-06-30T00:00:00').getTime();
        const now = new Date().getTime();

        if (now < activationDate) {
            e.preventDefault(); // Prevent modal from closing
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
